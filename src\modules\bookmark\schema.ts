import { z } from '@hono/zod-openapi';

// 基础 Bookmark 对象
export const bookmarkSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  user_id: z.string().openapi({ example: 'user-uuid' }),
  circle_id: z.string().openapi({ example: 'circle-uuid' }),
  created_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
  updated_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
});

export type Bookmark = z.infer<typeof bookmarkSchema>;

// Toggle 请求体：仅需 circleId
export const bookmarkToggleRequest = z.object({
  circleId: z.string().openapi({ example: 'circle-uuid' }),
});

export type BookmarkToggleInput = z.infer<typeof bookmarkToggleRequest>;

// 收藏列表查询参数
export const bookmarkListQuery = z.object({
  // 传统分页参数（向后兼容）
  page: z.coerce.number().min(1).default(1).openapi({ example: 1 }),
  pageSize: z.coerce
    .number()
    .min(1)
    .max(100)
    .default(20)
    .openapi({ example: 20 }),

  // 游标分页参数（性能优化）
  cursor: z.string().optional().openapi({
    example: 'eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ==',
    description: 'Base64编码的游标，用于游标分页。提供时将忽略page参数。',
  }),

  // 其他查询参数
  search: z.string().optional().openapi({ example: '工作室' }),
  sortBy: z
    .enum(['created_at', 'circle_name'])
    .default('created_at')
    .openapi({ example: 'created_at' }),
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc')
    .openapi({ example: 'desc' }),
});

export type BookmarkListQuery = z.infer<typeof bookmarkListQuery>;

// 收藏列表响应项
export const bookmarkListItem = z.object({
  id: z.string().openapi({ example: 'bookmark-uuid' }),
  created_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
  circle: z.object({
    id: z.string().openapi({ example: 'circle-uuid' }),
    name: z.string().openapi({ example: '某某工作室' }),
    category: z.string().default('unknown').openapi({
      example: 'original',
      description:
        '社团分类，暂时使用默认值 unknown，待 circles 表添加 category 字段后更新',
    }),
    urls: z.string().nullable().openapi({ example: '{"twitter":"@example"}' }),
    created_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
    updated_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
  }),
});

export type BookmarkListItem = z.infer<typeof bookmarkListItem>;

// 收藏列表响应
export const bookmarkListResponse = z.object({
  items: z.array(bookmarkListItem),

  // 传统分页信息（向后兼容）
  total: z.number().openapi({ example: 15 }),
  page: z.number().openapi({ example: 1 }),
  pageSize: z.number().openapi({ example: 20 }),
  totalPages: z.number().openapi({ example: 1 }),

  // 游标分页信息（性能优化）
  nextCursor: z.string().nullable().openapi({
    example: 'eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ==',
    description: '下一页的游标，为null表示没有更多数据',
  }),
  hasMore: z.boolean().openapi({
    example: true,
    description: '是否还有更多数据',
  }),
});

export type BookmarkListResponse = z.infer<typeof bookmarkListResponse>;

// 收藏状态响应
export const bookmarkStatusResponse = z.object({
  isBookmarked: z.boolean().openapi({ example: true }),
  bookmarkId: z.string().nullable().openapi({ example: 'bookmark-uuid' }),
  createdAt: z.string().nullable().openapi({ example: '2025-01-01T00:00:00Z' }),
});

export type BookmarkStatusResponse = z.infer<typeof bookmarkStatusResponse>;

// 收藏统计响应
export const bookmarkStatsResponse = z.object({
  totalBookmarks: z.number().openapi({ example: 15 }),
  recentBookmarks: z.number().openapi({ example: 3 }), // 最近7天新增
  categoryCounts: z.record(z.string(), z.number()).openapi({
    example: { original: 8, derivative: 7 },
  }),
  // 新增：收藏的社团ID列表（轻量级，用于快速状态检查）
  bookmarkedCircleIds: z
    .array(z.string())
    .optional()
    .openapi({
      example: ['circle-1', 'circle-2', 'circle-3'],
      description:
        '用户收藏的所有社团ID列表，仅在 includeIds=true 时返回，用于前端快速收藏状态检查',
    }),
});

export type BookmarkStatsResponse = z.infer<typeof bookmarkStatsResponse>;

// 收藏统计查询参数
export const bookmarkStatsQuery = z.object({
  includeIds: z
    .string()
    .optional()
    .transform((val) => val === 'true')
    .openapi({
      example: 'true',
      description: '是否包含收藏的社团ID列表，用于前端批量状态检查优化',
    }),
});

export type BookmarkStatsQuery = z.infer<typeof bookmarkStatsQuery>;

// 批量操作请求
export const bookmarkBatchRequest = z.object({
  action: z.enum(['add', 'remove']).openapi({ example: 'add' }),
  circleIds: z
    .array(z.string())
    .min(1)
    .max(50)
    .openapi({
      example: ['circle-1', 'circle-2', 'circle-3'],
    }),
});

export type BookmarkBatchRequest = z.infer<typeof bookmarkBatchRequest>;

// 批量操作响应
export const bookmarkBatchResponse = z.object({
  success: z.array(z.string()).openapi({
    example: ['circle-1', 'circle-2'],
  }),
  failed: z
    .array(
      z.object({
        circleId: z.string().openapi({ example: 'circle-3' }),
        reason: z.string().openapi({ example: '社团不存在' }),
      })
    )
    .openapi({
      example: [{ circleId: 'circle-3', reason: '社团不存在' }],
    }),
  total: z.number().openapi({ example: 3 }),
  successCount: z.number().openapi({ example: 2 }),
  failedCount: z.number().openapi({ example: 1 }),
});

export type BookmarkBatchResponse = z.infer<typeof bookmarkBatchResponse>;
