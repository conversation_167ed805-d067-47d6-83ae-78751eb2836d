-- BetterAuth 兼容的测试用户数据
-- 邮箱: <EMAIL>
-- 密码: password123

INSERT INTO user (id, name, email, email_verified, image, created_at, updated_at, username, role, locale) VALUES (
  'b173b285-8349-42a4-9b7e-435c1582e7b1',
  'Test User',
  '<EMAIL>',
  0,
  NULL,
  *************,
  *************,
  'test_user',
  'user',
  'en'
);

INSERT INTO account (id, account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires_at, refresh_token_expires_at, scope, password, created_at, updated_at) VALUES (
  '971a0077-a949-4ce8-9cc0-9f7de3af70e0',
  '<EMAIL>',
  'credential',
  'b173b285-8349-42a4-9b7e-435c1582e7b1',
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  '$2b$12$7rtsH1HxPiV4US5JsLQ0jewSVn3SPYVkl8dzyUaRsRdMKbsq1LPoK',
  *************,
  *************
);
