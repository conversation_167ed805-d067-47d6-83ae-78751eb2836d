import type { D1Database } from '@cloudflare/workers-types';

import { createCircleRepository } from './repository';
import type { Circle, CircleCreateInput, CircleUpdateInput } from './schema';
import { C<PERSON>, Logger } from '@/infrastructure';

/**
 * 列出社团，支持分页和搜索
 */
export async function listCircles(
  db: D1Database,
  options: {
    page?: number;
    pageSize?: number;
    search?: string;
    userId?: string; // 新增：用于个性化排序
  } = {},
  cache?: Cache,
  logger?: Logger
): Promise<{ items: Circle[]; total: number }> {
  const { page = 1, pageSize = 50, search, userId } = options;
  const offset = (page - 1) * pageSize;

  // 构建缓存键，包含所有查询参数
  // 注意：个性化结果不使用缓存，或者按用户分别缓存
  const cacheKey = userId
    ? `circles:user:${userId}:${JSON.stringify({ page, pageSize, search })}`
    : `circles:${JSON.stringify({ page, pageSize, search })}`;

  if (cache && !userId) {
    // 只有非个性化查询才使用全局缓存
    const cached = await cache.get<{ items: Circle[]; total: number }>(
      cacheKey
    );
    if (cached) {
      logger?.debug?.('listCircles: hit cache', { key: cacheKey });
      return cached;
    }
  }

  // 构建 WHERE 条件和搜索参数
  const conditions: string[] = [];
  const searchParams: any[] = [];

  if (search) {
    conditions.push(`(
      UPPER(c.name) LIKE UPPER(?) OR
      UPPER(json_extract(c.urls, '$.author')) LIKE UPPER(?)
    )`);
    searchParams.push(`%${search}%`, `%${search}%`);
  }

  const whereClause =
    conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  // 查询总数 - 只需要搜索参数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM circles c
    ${whereClause}
  `;

  const countParams = [...searchParams];
  const totalResult = await db
    .prepare(countQuery)
    .bind(...countParams)
    .first<{ total: number }>();
  const total = totalResult?.total || 0;

  // 查询数据
  const dataQuery = userId
    ? `
    SELECT
      c.id,
      c.name,
      c.urls,
      c.created_at,
      c.updated_at,
      CASE WHEN b.id IS NOT NULL THEN 1 ELSE 0 END as is_bookmarked
    FROM circles c
    LEFT JOIN bookmarks b ON c.id = b.circle_id AND b.user_id = ?
    ${whereClause}
    ORDER BY is_bookmarked DESC, c.name ASC
    LIMIT ? OFFSET ?
  `
    : `
    SELECT
      c.id,
      c.name,
      c.urls,
      c.created_at,
      c.updated_at
    FROM circles c
    ${whereClause}
    ORDER BY c.name ASC
    LIMIT ? OFFSET ?
  `;

  // 构建数据查询参数
  const dataParams = userId
    ? [userId, ...searchParams, pageSize, offset]
    : [...searchParams, pageSize, offset];

  const { results } = await db
    .prepare(dataQuery)
    .bind(...dataParams)
    .all();
  const items = results as Circle[];

  const result = { items, total };

  if (cache && !userId) {
    // 只缓存非个性化结果
    // 缓存时间根据是否有搜索条件调整
    const cacheTime = search ? 60 : 300; // 搜索结果缓存时间更短
    await cache.set(cacheKey, result, cacheTime);
    logger?.debug?.('listCircles: cached results', {
      key: cacheKey,
      count: items.length,
      total,
    });
  }

  return result;
}

/**
 * 创建社团
 */
export async function createCircle(
  db: D1Database,
  input: CircleCreateInput
): Promise<Circle> {
  const repo = createCircleRepository(db);
  return repo.create(input);
}

/**
 * 获取社团详情
 */
export async function getCircle(
  db: D1Database,
  id: string
): Promise<Circle | null> {
  const repo = createCircleRepository(db);
  return repo.findById(id);
}

/**
 * 更新社团
 */
export async function updateCircle(
  db: D1Database,
  id: string,
  input: CircleUpdateInput
): Promise<Circle | null> {
  const repo = createCircleRepository(db);
  return repo.update(id, input);
}

/**
 * 删除社团
 */
export async function deleteCircle(db: D1Database, id: string): Promise<void> {
  const repo = createCircleRepository(db);
  await repo.delete(id);
}
