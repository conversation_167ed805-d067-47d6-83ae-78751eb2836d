import { drizzle } from 'drizzle-orm/d1';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { betterAuth } from 'better-auth';
import * as schema from '../../../auth-schema';
import { betterAuthOptions } from './options';

// 全局 auth 实例缓存
let globalAuthInstance: ReturnType<typeof betterAuth> | null = null;
let globalEnvHash: string | null = null;

/**
 * 生成环境变量的哈希值，用于检测环境变化
 */
function getEnvHash(env: CloudflareBindings): string {
  return `${env.BETTER_AUTH_SECRET}-${env.BETTER_AUTH_URL}`;
}

/**
 * Better Auth Instance for Cloudflare D1
 * 使用单例模式确保整个应用使用同一个实例
 */
export const auth = (env: CloudflareBindings) => {
  const envHash = getEnvHash(env);

  // 如果环境变化或者还没有实例，创建新实例
  if (!globalAuthInstance || globalEnvHash !== envHash) {
    console.log('[Better Auth] Creating new auth instance');

    // 使用 Cloudflare D1 数据库
    const db = drizzle(env.DB);

    globalAuthInstance = betterAuth({
      ...betterAuthOptions,
      database: drizzleAdapter(db, {
        provider: 'sqlite',
        schema: schema,
      }), // D1 基于 SQLite
      baseURL: env.BETTER_AUTH_URL,
      secret: env.BETTER_AUTH_SECRET,
    });

    globalEnvHash = envHash;
  }

  return globalAuthInstance;
};
