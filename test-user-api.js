// 简单的测试脚本来验证用户 API 修复
async function testUserAPI() {
  const baseUrl = 'http://127.0.0.1:8787';

  try {
    console.log('🧪 测试用户 API...');

    // 1. 尝试登录管理员
    console.log('1. 尝试登录管理员...');
    const loginResponse = await fetch(`${baseUrl}/auth/sign-in/username`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // 重要：包含 cookies
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    console.log(`登录响应状态: ${loginResponse.status}`);

    if (loginResponse.status === 200) {
      const loginData = await loginResponse.json();
      console.log('✅ 登录成功');
      console.log('登录响应:', loginData);

      // 获取 cookies
      const cookies = loginResponse.headers.get('set-cookie');
      console.log('Cookies:', cookies);

      // 检查所有 cookies
      const allCookies = loginResponse.headers.getSetCookie?.() || [];
      console.log('所有 Cookies:', allCookies);

      // 1.5. 测试 Better Auth 会话端点
      console.log('1.5. 测试 Better Auth 会话端点...');
      const sessionResponse = await fetch(`${baseUrl}/auth/session`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          Authorization: `Bearer ${loginData.token}`,
          Cookie: `better-auth.session_token=${loginData.token}; ${cookies || ''}`,
        },
      });

      console.log(`会话响应状态: ${sessionResponse.status}`);
      if (sessionResponse.status === 200) {
        const sessionData = await sessionResponse.json();
        console.log('✅ 会话验证成功');
        console.log('会话数据:', sessionData);
      } else {
        const sessionError = await sessionResponse.text();
        console.log('❌ 会话验证失败');
        console.log('会话错误:', sessionError);
      }

      // 2. 测试 /admin/users API
      console.log('2. 测试 /admin/users API...');
      const usersResponse = await fetch(`${baseUrl}/admin/users`, {
        method: 'GET',
        credentials: 'include', // 重要：包含 cookies
        headers: {
          Authorization: `Bearer ${loginData.token}`, // 使用 Better Auth 返回的 token
          Cookie: `better-auth.session_token=${loginData.token}; ${cookies || ''}`, // 尝试设置 Better Auth 的默认 cookie 名称
        },
      });

      console.log(`用户列表响应状态: ${usersResponse.status}`);

      if (usersResponse.status === 200) {
        const users = await usersResponse.json();
        console.log('✅ 用户 API 工作正常');
        console.log('用户数量:', users.length);
        console.log('用户列表:', users);
      } else {
        const errorText = await usersResponse.text();
        console.log('❌ 用户 API 失败');
        console.log('错误响应:', errorText);
      }
    } else {
      const errorText = await loginResponse.text();
      console.log('❌ 登录失败');
      console.log('错误响应:', errorText);
    }
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testUserAPI();
